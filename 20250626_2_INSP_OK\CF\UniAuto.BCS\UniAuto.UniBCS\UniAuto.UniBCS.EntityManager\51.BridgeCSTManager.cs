using System;
using System.Collections;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Xml;

using UniAuto.UniBCS.Core;
using UniAuto.UniBCS.Entity;
using UniAuto.UniBCS.MISC;
using UniAuto.UniBCS.Log;
//20250624
using System.Xml.Linq; // Added for XML operations, though XmlDocument is used. Can be removed if not strictly needed.


using System.Timers;

namespace UniAuto.UniBCS.EntityManager
{
    public class BridgeCSTManager : EntityManager//,IDataSource
    {
        private Dictionary<string, cBridgeCST> _entities = new Dictionary<string, cBridgeCST>(); //string=PortID_CassetteID, Cassette=CST object
        //private Dictionary<string, Cassette> _incompEntities = new Dictionary<string, Cassette>(); //string=NodeNo_PortNo, Cassette=CST object
        
        
        public bool CheckBridgeCondition(Job _job, Equipment _eqp)
        {
            bool _result = false;
            string _s = string.Empty;
            string _trxKey = Helper.CreateTrxKey(); // 假設 Helper.CreateTrxKey() 存在

            try
            {
                ParameterManager para = Workbench.Instance.GetObject("ParameterManager") as ParameterManager;
                Line _line = ObjectManager.LineManager.GetLine(Workbench.ServerName);
                if (para != null && para.ContainsKey("BCS_NO_USE_BRIDGE_FUN") && para["BCS_NO_USE_BRIDGE_FUN"].GetBoolean())
                {
                    NLogManager.Logger.LogInfoWrite(LoggerName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()",
                        string.Format("Bridge 功能已禁用。GlassID<{0}>, NodeNo<{1}>。跳過橋接條件檢查。", _job._GlassID._BCSGlassID, _eqp.Data.NODENO));
                    return true; // Bridge 功能禁用，條件滿足。
                }

                // 13       '//--Check GlassID
                // 14       If Left(objWork.m_szGlassID, 3) = "BCM" Or Left(objWork.m_szGlassID, 3) = "GID" Then
                if (_job._GlassID._BCSGlassID.StartsWith("BCM") || _job._GlassID._BCSGlassID.StartsWith("GID"))
                {
                    _s = string.Format("GlassNo=<{0}>,GlassID=<{1}>is illegal, BC Will Send This Glass To Unloader", _job._GlassNumberHex, _job._GlassID._BCSGlassID);
                    NLogManager.Logger.LogWarnWrite(LoggerName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);
                    // Call MU_SendOPIPopMsg("Bridge Data Error", G_Log) - 暫時由日誌處理
                    return false; // 檢查失敗
                }

                // 24       '//---Check Process Function
                // 25       If objWork.m_nGlsProcFun <> 0 Then
                // 假設 eProcessFunction.NORMAL 對應 0
                if (_job._JobInfo._ProcessFunction != eProcessFunction.NORMAL)
                {
                    _s = string.Format("GlassNo=<{0}>,玻璃處理功能=<{1}>, BC Will Send This Glass To Unloader", _job._GlassNumberHex, _job._JobInfo._ProcessFunction.ToString());
                    NLogManager.Logger.LogWarnWrite(LoggerName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);
                    // Call MU_SendOPIPopMsg("Bridge Data Error", G_Log)
                    return false; // 檢查失敗
                }

                // 37       '20240501 [F240500026] INSP01流片邏輯修改
                // 38       If G_BC.m_cLine.m_szLineFlag = INSP_LINE_FLAG And Mid(strGlassData, 12, 1) = "A" And Trim(G_BC.m_cLine.m_szSetRunMode) = "BRIF" Then
                // 假設 _job._JobInfo._GlassJudge 包含相關數據，且索引從 0 開始
                if ((Workbench.Instance.LineType() == Globals.LineType.INSP_LINE_FLAG) && _job._JobInfo._GlassJudge.Length >= 12 && _job._JobInfo._GlassJudge.Substring(11, 1) == "A" && _line.File._LineRunMode == eRunMode.BRIF)
                {
                    _s = string.Format("GlassNo=<{0}>,GlassData Judge Code=<D>，BC Will Send This Glass To Unloader", _job._GlassNumberHex);
                    NLogManager.Logger.LogWarnWrite(LoggerName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);
                    // Call MU_SendOPIPopMsg("Bridge Data Error", G_Log)
                    return false; // 檢查失敗
                }

                // 47       Set objBridgeCST = G_BC.m_cLine.BridgeCSTBySeq(objWork.m_nCstSeqNo)
                //20250620 待修正，已修正
                //<
                cBridgeCST objBridgeCST = GetBridgeCSTBySeq(_job._JobData._CassetteSequenceNo);
                //>
                // 49       '//---No Record in BC,It's first glass
                // 50       If objBridgeCST Is Nothing Then
                if (objBridgeCST == null)
                {
                    // 52           '//---Check Bridge Condition Before MES Verify
                    // 53           MU_CheckBridgeCondition = UT_CheckBridgeConditionBF(objWork, strGlassData, strNodeNo)
                    _result = UT_CheckBridgeConditionBF(_job, _eqp);
                }
                // 55       Else
                else
                {
                    // 57           '//---如果已經存在但是尚未Verify，先移除舊的，在進行Verify，以防止舊的CSTSeq存在，Add by Feist 20071130
                    // 58           If objBridgeCST.m_nVerify = 0 Then
                    if (objBridgeCST.Verify == 0)
                    {
                        _s = string.Format("CSTID=<{0}>,CSTSeq=<{1}>,LineNo=<{2}> 已存在。但未驗證，BC 將刪除舊的並重新驗證",
                                            objBridgeCST._CSTID, objBridgeCST.CSTSeq, objBridgeCST.FromLineNo);
                        NLogManager.Logger.LogWarnWrite(LoggerName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);

                        // 63               strKeyBridge = G_BC.m_cLine.CU_GetBridgeCSTKey(objBridgeCST.m_nCSTSeq)
                        // 64               Call G_BC.m_cLine.DeleteBridgeCST(strKeyBridge, True)
                        // 假設 _line.CU_GetBridgeCSTKey 和 _line.DeleteBridgeCST 存在於 Line 物件上
                        // 這裡需要根據實際的 Line 類別實現來調整
                        // 由於 DeleteCassette 在 BridgeCSTManager 中，這裡可能需要調用 BridgeCSTManager 的方法
                        // 暫時假設 Line 類別有這些方法
                        //20250620 待修正，已修正
                        //<
                        string strKeyBridge = CU_GetBridgeCSTKey(objBridgeCST.CSTSeq);
                        DeleteBridgeCST(strKeyBridge, true); // 假設 true 表示移除已完成的 Job
                        //>

                        // 66               MU_CheckBridgeCondition = UT_CheckBridgeConditionBF(objWork, strGlassData, strNodeNo)
                        //20250620 待修正，已修正
                        //<
                        _result = UT_CheckBridgeConditionBF(_job, _eqp);
                        //>
                    }
                    // 69           '20210709 ITES Bridge
                    // 70           If Trim(G_BC.m_cLine.m_szLineFlag) = ITES_LINE_FLAG Then
                    else if ((Workbench.Instance.LineType() == Globals.LineType.ITES_LINE_FLAG))
                    {
                        // 71               MU_CheckBridgeCondition = UT_CheckBridgeVerifySICVAF(objBridgeCST, strGlassData, strNodeNo)
                        //20250620 待修正，已修正
                        //<
                        _result = UT_CheckBridgeVerifySICVAF(objBridgeCST, _job);
                        //>
                    }
                    // 72           Else
                    else
                    {
                        // 73               '//---Check Bridge Condition After MES Verify
                        // 74               MU_CheckBridgeCondition = UT_CheckBridgeConditionAF(objBridgeCST, objWork, strGlassData)
                        //20250620 待修正，已修正
                        //<
                        _result = UT_CheckBridgeConditionAF(objBridgeCST, _job);
                        //>
                    }
                }
            }
            catch (System.Exception ex)
            {
                NLogManager.Logger.LogErrorWrite(LoggerName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", ex);
                _result = false; // On Error GoTo ErrHandle
            }

            return _result;
        }

        private bool UT_CheckBridgeConditionBF(Job _job, Equipment _eqp)
        {
            cBridgeCST objBridgeCST = null;
            string _s ;

            Line _line = ObjectManager.LineManager.GetLine(Workbench.ServerName);
            if (_line == null)
            {
                NLogManager.Logger.LogErrorWrite(LoggerName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", "Line object is null.");
                return false;
            }
            bool verifyNgOccurred = false;
            try
            {
                //---Step1:Check Next Line Bridge Mode
                //20210709 ITES Bridge 不需要check
                if (!_line.File._NextLineBridgeModeON && Workbench.Instance.LineType() ==  Globals.LineType.ITES_LINE_FLAG)
                {
                    string G_Log = string.Format("Next Line ID=<{0}>, Bridge Mode=<{1}>,BC Will Set Bridge CST Verify NG.", _line.File._NextLineID, _line.File._NextLineBridgeModeON);
                    NLogManager.Logger.LogErrorWrite(LoggerName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", G_Log);
                    //goto JumpVerifyNG;
                    verifyNgOccurred = true;
                }

                //---Step2:Check Next Line Heatbeat
                //20210709 ITES Bridge 不需要check
                if (!verifyNgOccurred && !_line.File._NextLineHeartBit && Workbench.Instance.LineType() != Globals.LineType.ITES_LINE_FLAG)
                {
                    string G_Log = string.Format("Next Line ID=<{0}>, Heart Beat=<{1}>,BC Will Set Bridge CST Verify NG.", _line.File._NextLineID, _line.File._NextLineHeartBit);
                    NLogManager.Logger.LogErrorWrite(LoggerName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", G_Log);
                    //goto JumpVerifyNG;
                    verifyNgOccurred = true;
                }

                //---Step3:Check Control Mode
                //20210709 ITES Bridge 不需要check
                if (!verifyNgOccurred && _line.File.HostMode == eHostMode.OFFLINE && Workbench.Instance.LineType() != Globals.LineType.ITES_LINE_FLAG)
                {
                    string G_Log = string.Format("Next Line ID=<{0}>, Control Mode=<{1}>, BC Will Set Bridge CST Verify NG.", _line.File._NextLineID, _line.File.HostMode);
                    NLogManager.Logger.LogErrorWrite(LoggerName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", G_Log);
                    //goto JumpVerifyNG;
                    verifyNgOccurred = true;
                }

                if (verifyNgOccurred)
                {
                    string G_Log_NG = "Bridge Verify NG";
                    //20250620 待修正
                    //<
                    _s = string.Format("Bridge Verify NG. GlassID<{0}>, CassetteID<{1}>", _job._GlassID._BCSGlassID, _job._SourceCassette._CassetteID);
                    NLogManager.Logger.LogWarnWrite(LoggerName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", _s);

                    //SendOPITerminalMessage(_trxKey, eTerminalMessageType.Warning, Helper.GetDesc("BRIDGE_VERIFY_NG"), _s);
                    //Invoke(Globals.ServiceName.UIService, Globals.TrxName.OPI.PopupPigMessageReport, new object[] { _s });
                    //>

                    objBridgeCST = new cBridgeCST(); // Re-initialize objBridgeCST if it was null or already used

                    objBridgeCST.CSTSeq = int.Parse(_job._JobData._CassetteSequenceNo);
                    objBridgeCST._CSTID = _job._SourceCassette._CassetteID;
                    objBridgeCST.FromLineNo = int.Parse(_job._JobData._LineNumber);
                    objBridgeCST.Verify = 2;      //---0:Not Verify ,1:OK,2:NG ,Add by Feist 20071024
                    //20250620 待修正
                    //<
                    //_line.AddBridgeCST(objBridgeCST, true);
                    //>

                    return false; // Indicate failure
                }

                //---------------------Check Complete---------------------------
                //---Add ObjBridgeCST
                objBridgeCST = new cBridgeCST();

                objBridgeCST.CSTSeq = int.Parse(_job._JobData._CassetteSequenceNo);
                objBridgeCST._CSTID = _job._SourceCassette._CassetteID;
                objBridgeCST.FirstGlsNo = _job._GlassNumberHex;
                objBridgeCST.FromLineNo = int.Parse(_job._JobInfo._LineID);
                objBridgeCST.Verify = 0;      //---0:Not Verify ,1:OK,2:NG ,Add by Feist 20071024
                //20250620 待修正，已修正
                //<
                AddBridgeCST(objBridgeCST, true); // Assuming AddBridgeCST exists on Line object
                //>

                //---Send 737_2 Verify Bridge
                //20250620 待修正，已修正
                //<
                _line.File._BridgeVerifyGlassData = _job._RawData;
                //>
                _line.File._BridgeVerifyCSTSeq = int.Parse(_job._JobData._CassetteSequenceNo);
                _line.File._BridgeVerifyNodeNo = _eqp.Data.NODENO;

                //20120829 By Aaron For "INSP" Line & "BRIF" Mode
                if (Workbench.Instance.LineType() == Globals.LineType.INSP_LINE_FLAG && _line.File._LineRunMode == eRunMode.BRIF)
                {
                    //20240501 [F240500026] INSP01流片邏輯修改
                    //Call UT_CheckBridgeVerifyBRIF(objWork, strGlassData, strNodeNo)
                    //20250620 待修正，已修正
                    //<
                     UT_CheckBridgeVerifyBRIF(_job, _eqp); // Assuming this returns bool
                    //>
                }
                //20210709 ITES Bridge
                else if (Workbench.Instance.LineType() == Globals.LineType.ITES_LINE_FLAG)
                {
                    //20250620 待修正，已修正
                    //<
                    UT_CheckBridgeVerifySICV(_job, _eqp); // Assuming this returns bool
                    //>
                }
                else
                {
                    //20250620 待修正
                    //<
                    //ObjectManager.MES.ReportS7F37B(_job._GlassID._BCSGlassID);
                    //>
                }

                return true; // Default success if no specific condition leads to NG or other function call

            //JumpVerifyNG:
            //    string G_Log_NG = "Bridge Verify NG";
            //    //<
            //    //ObjectManager.OPI.SendOPIPopMsg(G_Log_NG, G_Log_NG); // Assuming G_Log is the message itself for OPI
            //    //>

            //    objBridgeCST = new cBridgeCST(); // Re-initialize objBridgeCST if it was null or already used

            //    objBridgeCST.CSTSeq = int.Parse(_job._JobData._CassetteSequenceNo);
            //    objBridgeCST._CSTID = _job._SourceCassette._CassetteID;
            //    objBridgeCST.FromLineNo = int.Parse(_job._JobData._LineNumber);
            //    objBridgeCST.Verify = 2;      //---0:Not Verify ,1:OK,2:NG ,Add by Feist 20071024
            //    //<
            //    //_line.AddBridgeCST(objBridgeCST, true);
            //    //>

            //    return false; // Indicate failure
            }
            catch (System.Exception ex)
            {
                string G_Log = string.Format("Err=<{0}-{1}>", ex.Message, ex.StackTrace);
                NLogManager.Logger.LogErrorWrite(LoggerName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", G_Log);
                return false; // Indicate failure
            }
        }

        private string UT_CheckBridgeVerifyBRIF(Job _job, Equipment eqp)
        {
            string szDlnGlsData = string.Empty;
            cBridgeCST objBridgeCST = null;
            string szFileName = string.Empty;
            string G_Log = string.Empty;
            string result = string.Empty; // Function return value
            bool verifyNgOccurred = false;

            Line _line = ObjectManager.LineManager.GetLine(Workbench.ServerName);
            if (_line == null)
            {
                G_Log = "Line object is null. Cannot proceed with Bridge Verify.";
                NLogManager.Logger.LogErrorWrite(LoggerName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", G_Log);
                return result;
            }

            try
            {
                // Line 10-16: objWork update
                // The VBA code re-fetches objWork here. However, in C# migration,
                // it's common to pass the Job object directly.
                // Assuming objWork passed in is the correct one to be updated.
                if (_job != null)
                {
                    //objWork._JobInfo._GlassDataOld = objWork._GlassID._BCSGlassID;
                    ObjectManager.JobManager.EnqueueSave(_job);
                }
                else
                {
                    G_Log = "Job is null. Cannot process glass data.";
                    NLogManager.Logger.LogErrorWrite(LoggerName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", G_Log);
                    //goto JumpVerifyNG;
                    verifyNgOccurred = true;
                }
                
                // Line 21-30: Step1:Check GlassData and Modify Data to CV3
                if (!verifyNgOccurred)
                {
                    if (!string.IsNullOrEmpty(_line.File._BridgeVerifyGlassData.Trim()) &&
                        _line.File._BridgeVerifyCSTSeq != 0 &&
                        _line.File._BridgeVerifyGlassData.Trim() != new string('0', 20))
                    {
                        szDlnGlsData = _line.File._BridgeVerifyGlassData.Substring(0, 15) + "4" + _line.File._BridgeVerifyGlassData.Substring(16, 4);
                    }
                    else
                    {
                        G_Log = string.Format("SendGlsData=<{0}>,SendCSTSeq=<{1}>Data error,BC Will Set Verify NG",
                                              _line.File._BridgeVerifyGlassData, _line.File._BridgeVerifyCSTSeq);
                        NLogManager.Logger.LogErrorWrite(LoggerName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", G_Log);
                        //goto JumpVerifyNG;
                        verifyNgOccurred = true;
                    }
                }

                // Line 32-39: Step2:Check Bridge CST Seq
                objBridgeCST = GetBridgeCSTBySeq(_line.File._BridgeVerifyCSTSeq.ToString());
                if (!verifyNgOccurred)
                {
                    if (objBridgeCST == null)
                    {
                        G_Log = string.Format("Can not find the Bridge CST,CSTSeq=<{0}>,", _line.File._BridgeVerifyCSTSeq);
                        NLogManager.Logger.LogErrorWrite(LoggerName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", G_Log);
                        //goto JumpVerifyNG;
                        verifyNgOccurred = true;
                    }
                }

                // Line 41-55: Step3:Merger Cross Line Data
                szFileName = string.Format("{0}_{1}", objBridgeCST._CSTID.Trim(), objBridgeCST.CSTSeq.ToString("0000"));

                // Original VBA: If UT_MergerBridgeDataBRIF(...) Then ... Else '/// GoTo JumpVerifyNG
                // Commenting out UT_MergerBridgeDataBRIF as it's not defined and was commented out in the SICVAF version.
                // bool mergerResult = UT_MergerBridgeDataBRIF(objWork, objBridgeCST._CSTID, objBridgeCST.CSTSeq, long.Parse(objWork._JobInfo._LineID), szFileName, objBridgeCST.CreateTime);
                // If (!mergerResult) { goto JumpVerifyNG; }
                if (UT_MergerBridgeDataBRIF(_job,objBridgeCST,szFileName) )
                {
                }


                // Line 57-77: Step4:將資料傳給MPLC_COMM寫入MPLC
                if (!verifyNgOccurred)
                {
                    if (!string.IsNullOrEmpty(szDlnGlsData))
                    {
                        // The VBA code has a duplicate objWork re-fetch and update here.
                        // Assuming it's a copy-paste error in VBA and the initial update is sufficient.
                        // If the intent was to update objWork based on szDlnGlsData, the logic is flawed.

                        result = szDlnGlsData.Trim();

                        // Step5:Set RGBCST Verify OK
                        objBridgeCST.Verify = 1;  //---0:Not Verify ,1:OK,2:NG
                        objBridgeCST.NextLinePID = _job._JobInfo._ProductID;
                        //20250623 待修正，已修正
                        Cassette _cst = ObjectManager.CassetteManager.GetCassette(_job._SourceCassette._CassetteID);
                        //20250625 待修正，已修正
                        //<
                        objBridgeCST.NextLineGroupCRT = _job._JobInfo._GroupCriteria.Replace(_cst._CSTMapInfo._OperationNo, _cst._CSTMapInfo._NXNXOPEID);
                        SaveBridgeCSTToFile(objBridgeCST); // Save the updated BridgeCST object
                        //>                        
                    }
                    else
                    {
                        //goto JumpVerifyNG;
                        verifyNgOccurred = true;
                    }
                }
                if (verifyNgOccurred)
                {
                    szDlnGlsData = _line.File._BridgeVerifyGlassData; // Re-assign szDlnGlsData for NG case.
                    // Update ObjBridgeCST
                    objBridgeCST = GetBridgeCSTBySeq(_line.File._BridgeVerifyCSTSeq.ToString());

                    if (objBridgeCST != null)
                    {
                        objBridgeCST.Verify = 2; //---0:Not Verify ,1:OK,2:NG
                        //20250623 待修正，已修正
                        //<
                        SaveBridgeCSTToFile(objBridgeCST);
                        //>
                    }
                    else
                    {
                        G_Log = string.Format("Can not find the Bridge CST,CSTSeq=<{0}>", _line.File._BridgeVerifyCSTSeq);
                        NLogManager.Logger.LogErrorWrite(LoggerName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", G_Log);
                    }
                     //20250623 待修正，已修正
                     //<
                     //UT_ReplyDispatchGlassDataBRIF
                     //>>
                }

                return result;
            }
            catch (System.Exception ex)
            {
                G_Log = string.Format("Err=<{0}-{1}>", ex.Message, ex.StackTrace);
                NLogManager.Logger.LogErrorWrite(LoggerName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", G_Log);
                return result;
            }

        //JumpVerifyNG:
        //    szDlnGlsData = _line.File._BridgeVerifyGlassData; // Re-assign szDlnGlsData for NG case.
        //    // Update ObjBridgeCST
        //    objBridgeCST = GetBridgeCSTBySeq(_line.File._BridgeVerifyCSTSeq.ToString());

        //    if (objBridgeCST != null)
        //    {
        //        objBridgeCST.Verify = 2; //---0:Not Verify ,1:OK,2:NG
        //        //<
        //        //MU_WF_BridgeCST(objBridgeCST);
        //        //>
        //    }
        //    else
        //    {
        //        G_Log = string.Format("Can not find the Bridge CST,CSTSeq=<{0}>", _line.File._BridgeVerifyCSTSeq);
        //        NLogManager.Logger.LogErrorWrite(LoggerName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", G_Log);
        //    }

        //    // VBA: Call UT_ReplyDispatchGlassDataBRIF(szDlnGlsData)
        //    // This was commented out in the main flow, but called here.
        //    // If it's not implemented in C#, it should remain commented out or removed.
        //    // UT_ReplyDispatchGlassDataBRIF(szDlnGlsData);

        //    return result;
        }

        private string UT_CheckBridgeVerifySICV(Job _job, Equipment eqp)
        {
            string szDlnGlsData = string.Empty;
            cBridgeCST objBridgeCST = null;
            string szFileName = string.Empty;
            string G_Log = string.Empty;
            string result = string.Empty; // Function return value
            bool verifyNgOccurred = false;

            Line _line = ObjectManager.LineManager.GetLine(Workbench.ServerName);
            if (_line == null)
            {
                G_Log = "Line object is null. Cannot proceed with Bridge Verify.";
                NLogManager.Logger.LogErrorWrite(LoggerName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", G_Log);
                return result;
            }

            try
            {
                // Line 10-16: objWork update
                // The VBA code re-fetches objWork here. However, in C# migration,
                // it's common to pass the Job object directly.
                // Assuming objWork passed in is the correct one to be updated.
                if (_job != null)
                {
                    //objWork._JobInfo._GlassDataOld = objWork._GlassID._BCSGlassID;
                    ObjectManager.JobManager.EnqueueSave(_job);
                }
                else
                {
                    G_Log = "Job is null. Cannot process glass data.";
                    NLogManager.Logger.LogErrorWrite(LoggerName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", G_Log);
                    //goto JumpVerifyNG;
                    verifyNgOccurred = true;
                }

                // Line 21-30: Step1:Check GlassData and Modify Data to CV3
                if (!verifyNgOccurred)
                {
                    if (!string.IsNullOrEmpty(_line.File._BridgeVerifyGlassData.Trim()) &&
                        _line.File._BridgeVerifyCSTSeq != 0 &&
                        _line.File._BridgeVerifyGlassData.Trim() != new string('0', 20))
                    {
                        szDlnGlsData = _line.File._BridgeVerifyGlassData.Substring(0, 15) + "4" + _line.File._BridgeVerifyGlassData.Substring(16, 4);
                    }
                    else
                    {
                        G_Log = string.Format("SendGlsData=<{0}>,SendCSTSeq=<{1}>Data error,BC Will Set Verify NG",
                                              _line.File._BridgeVerifyGlassData, _line.File._BridgeVerifyCSTSeq);
                        NLogManager.Logger.LogErrorWrite(LoggerName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", G_Log);
                        //goto JumpVerifyNG;
                        verifyNgOccurred = true;
                    }
                }

                // Line 32-39: Step2:Check Bridge CST Seq
                objBridgeCST = GetBridgeCSTBySeq(_line.File._BridgeVerifyCSTSeq.ToString());
                if (!verifyNgOccurred)
                {
                    if (objBridgeCST == null)
                    {
                        G_Log = string.Format("Can not find the Bridge CST,CSTSeq=<{0}>,", _line.File._BridgeVerifyCSTSeq);
                        NLogManager.Logger.LogErrorWrite(LoggerName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", G_Log);
                        //goto JumpVerifyNG;
                        verifyNgOccurred = true;
                    }
                }

                // Line 41-55: Step3:Merger Cross Line Data
                szFileName = string.Format("{0}_{1}", objBridgeCST._CSTID.Trim(), objBridgeCST.CSTSeq.ToString("0000"));

                // Original VBA: If UT_MergerBridgeDataBRIF(...) Then ... Else '/// GoTo JumpVerifyNG
                // Commenting out UT_MergerBridgeDataBRIF as it's not defined and was commented out in the SICVAF version.
                // bool mergerResult = UT_MergerBridgeDataBRIF(objWork, objBridgeCST._CSTID, objBridgeCST.CSTSeq, long.Parse(objWork._JobInfo._LineID), szFileName, objBridgeCST.CreateTime);
                // If (!mergerResult) { goto JumpVerifyNG; }
                if (UT_MergerBridgeDataBRIF(_job, objBridgeCST, szFileName))
                {
                }


                // Line 57-77: Step4:將資料傳給MPLC_COMM寫入MPLC
                if (!verifyNgOccurred)
                {
                    if (!string.IsNullOrEmpty(szDlnGlsData))
                    {
                        // The VBA code has a duplicate objWork re-fetch and update here.
                        // Assuming it's a copy-paste error in VBA and the initial update is sufficient.
                        // If the intent was to update objWork based on szDlnGlsData, the logic is flawed.

                        result = szDlnGlsData.Trim();

                        // Step5:Set RGBCST Verify OK
                        objBridgeCST.Verify = 1;  //---0:Not Verify ,1:OK,2:NG
                        objBridgeCST.NextLinePID = _job._JobInfo._ProductID;
                        //20250623 待修正，已修正
                        Cassette _cst = ObjectManager.CassetteManager.GetCassette(_job._SourceCassette._CassetteID);
                        //20250625 待修正，已修正
                        //<
                        objBridgeCST.NextLineGroupCRT = _job._JobInfo._GroupCriteria.Replace(_cst._CSTMapInfo._OperationNo, _cst._CSTMapInfo._NXNXOPEID);
                        SaveBridgeCSTToFile(objBridgeCST); // Save the updated BridgeCST object
                        //>                        
                    }
                    else
                    {
                        //goto JumpVerifyNG;
                        verifyNgOccurred = true;
                    }
                }
                if (verifyNgOccurred)
                {
                    szDlnGlsData = _line.File._BridgeVerifyGlassData; // Re-assign szDlnGlsData for NG case.
                    // Update ObjBridgeCST
                    objBridgeCST = GetBridgeCSTBySeq(_line.File._BridgeVerifyCSTSeq.ToString());

                    if (objBridgeCST != null)
                    {
                        objBridgeCST.Verify = 2; //---0:Not Verify ,1:OK,2:NG
                        //20250623 待修正，已修正
                        //<
                        SaveBridgeCSTToFile(objBridgeCST);
                        //>
                    }
                    else
                    {
                        G_Log = string.Format("Can not find the Bridge CST,CSTSeq=<{0}>", _line.File._BridgeVerifyCSTSeq);
                        NLogManager.Logger.LogErrorWrite(LoggerName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", G_Log);
                    }
                    //20250623 待修正，已修正
                    //<
                    //UT_ReplyDispatchGlassDataBRIF
                    //>>
                }

                return result;
            }
            catch (System.Exception ex)
            {
                G_Log = string.Format("Err=<{0}-{1}>", ex.Message, ex.StackTrace);
                NLogManager.Logger.LogErrorWrite(LoggerName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", G_Log);
                return result;
            }

            //JumpVerifyNG:
            //    szDlnGlsData = _line.File._BridgeVerifyGlassData; // Re-assign szDlnGlsData for NG case.
            //    // Update ObjBridgeCST
            //    objBridgeCST = GetBridgeCSTBySeq(_line.File._BridgeVerifyCSTSeq.ToString());

            //    if (objBridgeCST != null)
            //    {
            //        objBridgeCST.Verify = 2; //---0:Not Verify ,1:OK,2:NG
            //        //<
            //        //MU_WF_BridgeCST(objBridgeCST);
            //        //>
            //    }
            //    else
            //    {
            //        G_Log = string.Format("Can not find the Bridge CST,CSTSeq=<{0}>", _line.File._BridgeVerifyCSTSeq);
            //        NLogManager.Logger.LogErrorWrite(LoggerName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", G_Log);
            //    }

            //    // VBA: Call UT_ReplyDispatchGlassDataBRIF(szDlnGlsData)
            //    // This was commented out in the main flow, but called here.
            //    // If it's not implemented in C#, it should remain commented out or removed.
            //    // UT_ReplyDispatchGlassDataBRIF(szDlnGlsData);

            //    return result;
        }

        private bool UT_MergerBridgeDataBRIF(Job _job, cBridgeCST objBridgeCST, string szFileName)
        {
            long lGlsNo;
            string szGlsNoHex;
            int intTemp;
            XmlDocument TempDoc = new XmlDocument();
            XmlElement XmlElement = null;
            string szFSPath = string.Empty;
            string szFSPath1 = string.Empty;
            string szMQPath = string.Empty;
            int nCount = 0;

            string _BridgeDrive = string.Empty;
            string _BridgeNextDrive = string.Empty;
            try
            {
                ParameterManager para = Workbench.Instance.GetObject("ParameterManager") as ParameterManager;
                bool result = false;
                if (para.ContainsKey("BCS_LOG_DRIVE"))
                {
                    _BridgeDrive = para["BCS_LOG_DRIVE"].GetString();
                    _BridgeDrive = string.Format(@"{0}:\", _BridgeDrive.Replace(":", "").Replace("\\", ""));
                    string _BridgeDriveFreeSize = FreeDiskSize(_BridgeDrive).GetIntString();

                    if (!_BridgeDriveFreeSize.NullOrEmpty())
                    {
                        if (Convert.ToDouble(_BridgeDriveFreeSize) / (Math.Pow(1024, 3)) < 10)
                        {
                            //20250624 待修正
                            //SendOPITerminalMessage(_trxKey, eTerminalMessageType.Alarm, Helper.GetDesc("SEVEREINFO"), string.Format("Log Drive Size(D槽): {0} GB, Please Contact CIM.", (Convert.ToDouble(_BridgeDriveFreeSize) / (Math.Pow(1024, 3)))));
                        }
                    }
                }

                if (para.ContainsKey("BCS_BRIDGE_DRIVE"))
                {
                    _BridgeNextDrive = para["BCS_BRIDGE_DRIVE"].GetString();
                    _BridgeNextDrive = string.Format(@"{0}:\", _BridgeNextDrive.Replace(":", "").Replace("\\", ""));
                    string _BridgeDriveFreeSize = FreeDiskSize(_BridgeNextDrive).GetIntString();

                    if (!_BridgeDriveFreeSize.NullOrEmpty())
                    {
                        if (Convert.ToDouble(_BridgeDriveFreeSize) / (Math.Pow(1024, 3)) < 10)
                        {
                            //20250624 待修正
                            //SendOPITerminalMessage(_trxKey, eTerminalMessageType.Alarm, Helper.GetDesc("SEVEREINFO"), string.Format("Log Drive Size(D槽): {0} GB, Please Contact CIM.", (Convert.ToDouble(_BridgeDriveFreeSize) / (Math.Pow(1024, 3)))));
                        }
                    }
                }

                // 20111019 modify by wenjou.hsu , for bridge file save in next line path.
                // 若下一條line 的路徑 可以連,就save 至next line 位置的download 位置,
                // 若不能連,則save 在local 端的upload 目錄下. ( FS comm 要30 秒做一次)
                if (!Directory.Exists(_BridgeNextDrive + "BRIDGE\\DOWNLOAD\\"))
                {
                    szFSPath = _BridgeDrive + "BRIDGE\\UPLOAD\\" + szFileName + ".xml";
                }
                else
                {
                    szFSPath = _BridgeNextDrive + "BRIDGE\\DOWNLOAD\\" + szFileName + ".xml";
                    NLogManager.Logger.LogWarnWrite(LoggerName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", "Save Bridge file to " + szFSPath + "OK");
                }

                if (Directory.Exists(_BridgeNextDrive + "BRIDGE\\ReceiveQ\\"))
                {
                    szFSPath1 = _BridgeNextDrive + "BRIDGE\\ReceiveQ\\" + szFileName + ".xml";
                    NLogManager.Logger.LogWarnWrite(LoggerName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", "Save Bridge file to " + szFSPath1 + "OK");
                }

                szMQPath = _BridgeDrive + "BRIDGE\\SENDQ\\" + szFileName + ".xml";

                for (intTemp = 1; intTemp <= 48; intTemp++)
                {
                    lGlsNo = int.Parse(_job._JobData._LineNumber) * 16384 + int.Parse(_job._JobData._CassetteSequenceNo) * 64 + intTemp;
                    szGlsNoHex = lGlsNo.ToString("X"); // Hex(lGlsNo)

                    // Set objWork = G_WIP.WorkItemByNoHex(szGlsNoHex)
                    // Assuming G_WIP maps to ObjectManager.JobManager and WorkItemByNoHex to GetJobByGlassNumberHex
                    _job = ObjectManager.JobManager.GetJobByNo(szGlsNoHex);

                    if (_job == null)
                    {
                        continue; // GoTo JumpNext
                    }

                    //20131008 Aaron : 新增判斷條件相同CreateTime才可Addition to Bridge XML
                    // If DateDiff("d", objWork.m_szCreateTime, Now) > 3 Then GoTo JumpNext
                    if ((DateTime.Now - _job.CreateTime).TotalDays > 3)
                    {
                        continue; // GoTo JumpNext
                    }

                    nCount = nCount + 1; //---Record Count for XML Decode Use

                    //---First Glass Create Head
                    if (nCount == 1)
                    {
                        //-- Create Node
                        XmlElement = TempDoc.CreateElement("CrossLineData");

                        //-- Add Node to Xml Document
                        TempDoc.AppendChild(XmlElement);

                        //---Merger Public Information to XML  'bygogi 20091222 add for 記錄副檔名以供MQ Comm Creste
                        UT_Merger_PublicData(XmlElement, szFileName + ".xml");

                        //---Merger Glass Data to XML
                        UT_MergerBridgeDataBRIFIXM(_job, XmlElement, intTemp);
                    }
                    else
                    {
                        //---Merger Cross Line XML
                        UT_MergerBridgeDataBRIFIXM(_job, XmlElement, intTemp);
                    }
                }

                //---Update Count
                UT_Update_XMLData(TempDoc, "CrossLineData/PUBLIC/GlsCount", nCount.ToString("00"));
                TempDoc.Save(szMQPath);

                if (!string.IsNullOrEmpty(szFSPath))
                {
                    TempDoc.Save(szFSPath);
                }

                if (!string.IsNullOrEmpty(szFSPath1))
                {
                    TempDoc.Save(szFSPath1);
                }

                NLogManager.Logger.LogWarnWrite(LoggerName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", "Save Bridge file FSpath,FSpath1,MQpath OK");

                result = true;

                //20130326 - *********:INSP Bridge to FAIC Read GlassID By Aaron.
                // Assuming G_BC.m_bItoFCrDCRXML maps to Globals.BC.m_bItoFCrDCRXML
                Line _line = ObjectManager.LineManager.GetLine(Workbench.ServerName);
                _line.File._BItoFCrDCRXML = true;

                return result;
            }
            catch (Exception ex)
            {
                // ErrHandle:
                NLogManager.Logger.LogErrorWrite(LoggerName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", string.Format("Err=<{0}-{1}>", ex.Message, ex.StackTrace));
                return false;
            }
        }

        private bool UT_MergerBridgeDataBRIF_BySlotID(Job _job, cBridgeCST objBridgeCST, string szFileName)
        {
            long lGlsNo;
            string szGlsNoHex;
            int intTemp;
            XmlDocument TempDoc = new XmlDocument();
            XmlElement XmlElement = null;
            string szFSPath = string.Empty;
            string szFSPath1 = string.Empty;
            string szMQPath = string.Empty;
            int nCount = 0;

            string _BridgeDrive = string.Empty;
            string _BridgeNextDrive = string.Empty;
            try
            {
                ParameterManager para = Workbench.Instance.GetObject("ParameterManager") as ParameterManager;
                bool result = false;
                if (para.ContainsKey("BCS_LOG_DRIVE"))
                {
                    _BridgeDrive = para["BCS_LOG_DRIVE"].GetString();
                    _BridgeDrive = string.Format(@"{0}:\", _BridgeDrive.Replace(":", "").Replace("\\", ""));
                    string _BridgeDriveFreeSize = FreeDiskSize(_BridgeDrive).GetIntString();

                    if (!_BridgeDriveFreeSize.NullOrEmpty())
                    {
                        if (Convert.ToDouble(_BridgeDriveFreeSize) / (Math.Pow(1024, 3)) < 10)
                        {
                            //20250624 待修正
                            //SendOPITerminalMessage(_trxKey, eTerminalMessageType.Alarm, Helper.GetDesc("SEVEREINFO"), string.Format("Log Drive Size(D槽): {0} GB, Please Contact CIM.", (Convert.ToDouble(_BridgeDriveFreeSize) / (Math.Pow(1024, 3)))));
                        }
                    }
                }

                if (para.ContainsKey("BCS_BRIDGE_DRIVE"))
                {
                    _BridgeNextDrive = para["BCS_BRIDGE_DRIVE"].GetString();
                    _BridgeNextDrive = string.Format(@"{0}:\", _BridgeNextDrive.Replace(":", "").Replace("\\", ""));
                    string _BridgeDriveFreeSize = FreeDiskSize(_BridgeNextDrive).GetIntString();

                    if (!_BridgeDriveFreeSize.NullOrEmpty())
                    {
                        if (Convert.ToDouble(_BridgeDriveFreeSize) / (Math.Pow(1024, 3)) < 10)
                        {
                            //20250624 待修正
                            //SendOPITerminalMessage(_trxKey, eTerminalMessageType.Alarm, Helper.GetDesc("SEVEREINFO"), string.Format("Log Drive Size(D槽): {0} GB, Please Contact CIM.", (Convert.ToDouble(_BridgeDriveFreeSize) / (Math.Pow(1024, 3)))));
                        }
                    }
                }

                // 20111019 modify by wenjou.hsu , for bridge file save in next line path.
                // 若下一條line 的路徑 可以連,就save 至next line 位置的download 位置,
                // 若不能連,則save 在local 端的upload 目錄下. ( FS comm 要30 秒做一次)
                if (!Directory.Exists(_BridgeNextDrive + "BRIDGE\\DOWNLOAD\\"))
                {
                    szFSPath = _BridgeDrive + "BRIDGE\\UPLOAD\\" + szFileName + ".xml";
                }
                else
                {
                    szFSPath = _BridgeNextDrive + "BRIDGE\\DOWNLOAD\\" + szFileName + ".xml";
                    NLogManager.Logger.LogWarnWrite(LoggerName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", "Save Bridge file to " + szFSPath + "OK");
                }

                if (Directory.Exists(_BridgeNextDrive + "BRIDGE\\ReceiveQ\\"))
                {
                    szFSPath1 = _BridgeNextDrive + "BRIDGE\\ReceiveQ\\" + szFileName + ".xml";
                    NLogManager.Logger.LogWarnWrite(LoggerName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", "Save Bridge file to " + szFSPath1 + "OK");
                }

                szMQPath = _BridgeDrive + "BRIDGE\\SENDQ\\" + szFileName + ".xml";

                for (intTemp = 1; intTemp <= 48; intTemp++)
                {
                    lGlsNo = int.Parse(_job._JobData._LineNumber) * 16384 + int.Parse(_job._JobData._CassetteSequenceNo) * 64 + intTemp;
                    szGlsNoHex = lGlsNo.ToString("X"); // Hex(lGlsNo)

                    // Set objWork = G_WIP.WorkItemByNoHex(szGlsNoHex)
                    // Assuming G_WIP maps to ObjectManager.JobManager and WorkItemByNoHex to GetJobByGlassNumberHex
                    _job = ObjectManager.JobManager.GetJobByNo(szGlsNoHex);

                    if (_job == null)
                    {
                        continue; // GoTo JumpNext
                    }

                    //20131008 Aaron : 新增判斷條件相同CreateTime才可Addition to Bridge XML
                    // If DateDiff("d", objWork.m_szCreateTime, Now) > 3 Then GoTo JumpNext
                    if ((DateTime.Now - _job.CreateTime).TotalDays > 3)
                    {
                        continue; // GoTo JumpNext
                    }

                    nCount = nCount + 1; //---Record Count for XML Decode Use

                    //---First Glass Create Head
                    if (nCount == 1)
                    {
                        //-- Create Node
                        XmlElement = TempDoc.CreateElement("CrossLineData");

                        //-- Add Node to Xml Document
                        TempDoc.AppendChild(XmlElement);

                        //---Merger Public Information to XML  'bygogi 20091222 add for 記錄副檔名以供MQ Comm Creste
                        UT_Merger_PublicData(XmlElement, szFileName + ".xml");

                        //---Merger Glass Data to XML
                        UT_MergerBridgeDataBRIFIXM(_job, XmlElement, intTemp);
                    }
                    else
                    {
                        //---Merger Cross Line XML
                        UT_MergerBridgeDataBRIFIXM(_job, XmlElement, intTemp);
                    }
                }

                //---Update Count
                UT_Update_XMLData(TempDoc, "CrossLineData/PUBLIC/GlsCount", nCount.ToString("00"));
                TempDoc.Save(szMQPath);

                if (!string.IsNullOrEmpty(szFSPath))
                {
                    TempDoc.Save(szFSPath);
                }

                if (!string.IsNullOrEmpty(szFSPath1))
                {
                    TempDoc.Save(szFSPath1);
                }

                NLogManager.Logger.LogWarnWrite(LoggerName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", "Save Bridge file FSpath,FSpath1,MQpath OK");

                result = true;

                //20130326 - *********:INSP Bridge to FAIC Read GlassID By Aaron.
                // Assuming G_BC.m_bItoFCrDCRXML maps to Globals.BC.m_bItoFCrDCRXML
                Line _line = ObjectManager.LineManager.GetLine(Workbench.ServerName);
                _line.File._BItoFCrDCRXML = true;

                return result;
            }
            catch (Exception ex)
            {
                // ErrHandle:
                NLogManager.Logger.LogErrorWrite(LoggerName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", string.Format("Err=<{0}-{1}>", ex.Message, ex.StackTrace));
                return false;
            }
        }

        private string FreeDiskSize()
        {
            return FreeDiskSize(string.Empty);
        }
        private string FreeDiskSize(string _assign = "")
        {
            //https://msdn.microsoft.com/en-us/library/system.io.driveinfo.availablefreespace(v=vs.110).aspx

            string _s = string.Empty;

            string _sysDrive = (_assign.NullOrEmpty() ? System.IO.Path.GetPathRoot(Environment.SystemDirectory) : _assign);

            System.IO.DriveInfo[] _allDrives = System.IO.DriveInfo.GetDrives();

            foreach (System.IO.DriveInfo _d in _allDrives)
            {
                if (_d.Name != _sysDrive) continue;

                _s = string.Format("{0:#} bytes", _d.AvailableFreeSpace);
                //_s = string.Format("{0:D15} bytes", _d.TotalFreeSpace);
                //_s = string.Format("{0:D15} bytes", _d.TotalSize);
                break;
            }

            return _s;
        }
        private bool UT_Merger_PublicData(XmlElement Parent_Element, string FileName = "")
        {
            Line _line = ObjectManager.LineManager.GetLine(Workbench.ServerName);
            XmlElement ElementLv1;
            XmlElement ElementLv2;
            int nTemp;

            //---Level 1
            ElementLv1 = CreateElement(Parent_Element, "PUBLIC", "List");

            //--Level 2
            CreateElement(ElementLv1, "CreateTime", "A", 14, DateTime.Now.ToString("yyyyMMddHHmmss"));
            CreateElement(ElementLv1, "NextLineID", "A", 8, _line.File._NextLineID);
            CreateElement(ElementLv1, "FromLineID", "A", 8, _line.Data.LINEID);
            CreateElement(ElementLv1, "FileName", "A", 12, FileName);
            CreateElement(ElementLv1, "GlsCount", "A", 2, "01"); //Set Default ,等計算完再update

            //---20120829 Merger OfflineSampling Information to XML
            //---Level 1
            if (Workbench.Instance.LineType() == Globals.LineType.INSP_LINE_FLAG && _line.File._LineRunMode == eRunMode.BRIF)
            {
                ElementLv1 = CreateElement(Parent_Element, "OFFLINESAMPLING", "List", 2);
            }
            //20210709 ITES Bridge
            else if (Workbench.Instance.LineType() == Globals.LineType.ITES_LINE_FLAG)
            {
                ElementLv1 = CreateElement(Parent_Element, "OFFLINESAMPLING", "List", 2);
            }

            return true;
        }
        // Helper function to create XML elements
        private XmlElement CreateElement(XmlElement Parent_Element, string szName, string szType = "", int nLen = 0, string szValue = "")
        {
            XmlElement New_Element = null;

            //--Create New Element
            New_Element = Parent_Element.OwnerDocument.CreateElement(szName);

            //--Set Element Value
            if (!string.IsNullOrEmpty(szType))
            {
                UT_AddElm(New_Element, szName, szType, nLen, szValue);
            }

            //--Add Element to then Parent Element
            Parent_Element.AppendChild(New_Element);

            return New_Element;
        }
        private void UT_AddElm(XmlElement NewElement, string szName, string szType, int nLen, string szValue)
        {
            XmlAttribute nameAttr = NewElement.OwnerDocument.CreateAttribute("name");
            nameAttr.Value = szName;
            NewElement.Attributes.Append(nameAttr);

            XmlAttribute typeAttr = NewElement.OwnerDocument.CreateAttribute("type");
            typeAttr.Value = szType;
            NewElement.Attributes.Append(typeAttr);

            XmlAttribute lenAttr = NewElement.OwnerDocument.CreateAttribute("len");
            lenAttr.Value = nLen.ToString();
            NewElement.Attributes.Append(lenAttr);

            XmlAttribute valueAttr = NewElement.OwnerDocument.CreateAttribute("value");
            valueAttr.Value = szValue;
            NewElement.Attributes.Append(valueAttr);
        }
        private bool UT_MergerBridgeDataBRIFIXM(Job _job, XmlElement Parent_Element, int intTemp)
        {
            XmlElement ElementLv1 = null;
            XmlElement ElementLv2 = null;
            string szSlot = "";
            int nTemp = 0;
            string szTemp = "";

            try
            {
                //---Level 1
                szSlot = "Slot" + string.Format("{0:000}", intTemp);
                ElementLv1 = CreateElement(Parent_Element, szSlot, "List");

                //--Level 2
                if (_job != null)
                {
                    CreateElement(ElementLv1, "GlassNoDec", "A", _job._JobData._GlassNumber.ToString().Length, _job._JobData._GlassNumber.ToString());
                    CreateElement(ElementLv1, "GlassNoHex", "A", _job._JobData._GlassNumber.Length, _job._JobData._GlassNumber);

                    //20110308
                    //20110406 MBY CTYU
                    if (!string.IsNullOrEmpty(_job._RawData) && _job._RawData.Substring(0, 4) != "0000")
                    {
                        CreateElement(ElementLv1, "GlassDataOld", "A", _job._RawData.Length, _job._RawData);
                    }
                    else
                    {
                        CreateElement(ElementLv1, "GlassDataOld", "A", _job._RawData.Length, _job._RawData);
                        // Log
                    }
                    Cassette _cst = ObjectManager.CassetteManager.GetCassette(_job._SourceCassette._CassetteID);
                    //--- 剩下的資訊由Glass Data拆解
                    //--- From S765A(原本work帶的資訊)
                    //--- Group CRT 
                    //20250625 待修正，已修正
                    CreateElement(ElementLv1, "GroupCRT", "A", _job._JobInfo._GroupCriteria.Replace(_cst._CSTMapInfo._OperationNo, _cst._CSTMapInfo._NXNXOPEID).Length, _job._JobInfo._GroupCriteria.Replace(_cst._CSTMapInfo._OperationNo, _cst._CSTMapInfo._NXNXOPEID));// Not found mapping
                    //--- GroupID
                    CreateElement(ElementLv1, "GroupID", "A", _job._JobInfo._GroupID.Length, _job._JobInfo._GroupID);
                    //--- PRODID
                    CreateElement(ElementLv1, "ProductID", "A", _job._JobInfo._ProductID.Length, _job._JobInfo._ProductID);
                    //--- OPID
                    CreateElement(ElementLv1, "OPID", "A", _cst._CSTMapInfo._NXNXOPEID.Length, _cst._CSTMapInfo._NXNXOPEID);
                    //--- Host OPE_NO
                    CreateElement(ElementLv1, "HostOPNO", "A", _cst._CSTMapInfo._NXNXOPEID.Length, _cst._CSTMapInfo._NXNXOPEID);
                    //--- Proc ID
                    CreateElement(ElementLv1, "ProcID", "A", _cst._CSTMapInfo._NXNXOPEID.Length, _cst._CSTMapInfo._NXNXOPEID);
                    //--- PID
                    CreateElement(ElementLv1, "PID", "A", _cst._CSTMapInfo._RecipeID.Length, _cst._CSTMapInfo._RecipeID);
                    //--- JudegeRule
                    CreateElement(ElementLv1, "GlassJudgeRule", "A", _job._JobInfo._GlassJudgeRule.Length, _job._JobInfo._GlassJudgeRule);
                    //--- Process Function
                    CreateElement(ElementLv1, "ProcessFunction", "A", _job._JobInfo._ProductFlag.Length, _job._JobInfo._ProductFlag);
                    //--- OwnerID
                    CreateElement(ElementLv1, "OwnerID", "A", _cst._CSTMapInfo._OwnerID.Length, _cst._CSTMapInfo._OwnerID);
                    //--- RouteID
                    CreateElement(ElementLv1, "RouteID", "A", _cst._CSTMapInfo._RouteID.Length, _cst._CSTMapInfo._RouteID);
                    //--- NGCRT
                    CreateElement(ElementLv1, "NGPortCRT", "A", _cst._CSTMapInfo._NGPortCriteria.Length, _cst._CSTMapInfo._NGPortCriteria);
                    //--- Product Flag
                    CreateElement(ElementLv1, "ProdFlag", "A", _job._JobInfo._ProductFlag.Length, _job._JobInfo._ProductFlag);
                    //--- Min Panel NG Count
                    CreateElement(ElementLv1, "MinNGPanelCount", "A", _cst._CSTMapInfo._MinNGPanelCount.Length, _cst._CSTMapInfo._MinNGPanelCount);
                    //--- CST Reserve
                    CreateElement(ElementLv1, "CSTResv", "A", _cst._CSTMapInfo._CstResvSrc.Length, _cst._CSTMapInfo._CstResvSrc);

                    //--- From S765A(原本work帶的資訊)
                    //20250625 待修正
                    CreateElement(ElementLv1, "CSTInfo", "A", 0, _cst._CSTSlot._EQPSlotInfo);
                    CreateElement(ElementLv1, "WorkInfo", "A", 0, _cst._CSTSlot._BCSWorkInfo);
                    CreateElement(ElementLv1, "GlassGrade", "A", _job._JobInfo._GlassGrade.Length, _job._JobInfo._GlassGrade);
                    CreateElement(ElementLv1, "TestReportType", "A", _job._JobInfo._TestReportType.Length, _job._JobInfo._TestReportType);
                    CreateElement(ElementLv1, "ReworkType", "A", _job._JobInfo._ReworkType.Length, _job._JobInfo._ReworkType);
                    CreateElement(ElementLv1, "RGBReworkCount", "A", _job._JobInfo._RGBReworkCount.Length, _job._JobInfo._RGBReworkCount);
                    CreateElement(ElementLv1, "ITOReworkCount", "A", _job._JobInfo._ITOReworkCount.Length, _job._JobInfo._ITOReworkCount);
                    CreateElement(ElementLv1, "MaxRGBReworkCount", "A", _job._JobInfo._MaxRGBReworkCount.Length, _job._JobInfo._MaxRGBReworkCount);
                    CreateElement(ElementLv1, "MaxITOReworkCount", "A", _job._JobInfo._MaxITOReworkCount.Length, _job._JobInfo._MaxITOReworkCount);
                    CreateElement(ElementLv1, "SortFlag", "A", _job._JobInfo._SortFlag.Length, _job._JobInfo._SortFlag);
                    CreateElement(ElementLv1, "InitialCheckFlag", "A", _job._JobInfo._InitialCheckFlag.Length, _job._JobInfo._InitialCheckFlag);
                    CreateElement(ElementLv1, "TargetEQP", "A", _job._JobInfo._SortFlag.Length, _job._JobInfo._SortFlag);
                    CreateElement(ElementLv1, "DefectCode", "A", _job._JobInfo._DefectCode.Length, _job._JobInfo._DefectCode);
                    CreateElement(ElementLv1, "DefectType", "A", _job._JobInfo._DefectType.Length, _job._JobInfo._DefectType);
                    CreateElement(ElementLv1, "PSFlag", "A", _job._JobInfo._PSFlag.Length, _job._JobInfo._PSFlag);
                    CreateElement(ElementLv1, "GlsResv", "A", _job._JobInfo._GlsResvSrc.Length, _job._JobInfo._GlsResvSrc);
                    CreateElement(ElementLv1, "BufferSlot", "A", _job._JobInfo._BufferSlot.Length, _job._JobInfo._BufferSlot);
                    CreateElement(ElementLv1, "MGVID", "A", 0, ""); // Not found mapping

                    //--- Some Items Must Keep Host Downloaded Data
                    CreateElement(ElementLv1, "HostPortID", "A", _job._FromPortID.Length, _job._FromPortID);
                    CreateElement(ElementLv1, "HostSlotID", "A", _job._FromSlotNo.ToString().Length, _job._FromSlotNo.ToString());
                    CreateElement(ElementLv1, "HostGlassID", "A", _job._GlassID._BCSGlassID.Length, _job._GlassID._BCSGlassID);
                    CreateElement(ElementLv1, "HostPanelInfo", "A", _job._JobInfo._HostPanelInfo.Length, _job._JobInfo._HostPanelInfo);
                    CreateElement(ElementLv1, "HostNGMarkCode", "A", _job._JobInfo._HostNGMark.Length, _job._JobInfo._HostNGMark);
                    CreateElement(ElementLv1, "HostNGMarkReasonCode", "A", _job._JobInfo._NGMarkReasonCode.Length, _job._JobInfo._NGMarkReasonCode);
                    CreateElement(ElementLv1, "HostJudgeCode", "A", _job._JobInfo._GlassJudge.Length, _job._JobInfo._GlassJudge);
                    CreateElement(ElementLv1, "HostCoaterHst", "A", _job._JobInfo._CoaterHistory.Length, _job._JobInfo._CoaterHistory);

                    ElementLv2 = CreateElement(ElementLv1, "AlignerHstCount", "List", 1, _job._JobInfo._AlignerHistory.Count.ToString());
                    if (_job._JobInfo._AlignerHistory.Count > 0)
                    {
                        for (int n = 0; n <= _job._JobInfo._AlignerHistory.Count; n++)
                        {
                            string szHostAlignerHst = "HostAlignerHst" + n.ToString();
                            CreateElement(ElementLv2, szHostAlignerHst, "A", _job._JobInfo._AlignerHistory[n].Item1.Length, _job._JobInfo._AlignerHistory[n].Item1.ToString());
                        }
                    }
                    ElementLv2 = CreateElement(ElementLv1, "AlignerOPEIDCount", "List", 1, _job._JobInfo._AlignerHistory.Count.ToString());
                    if (_job._JobInfo._AlignerHistory.Count > 0)
                    {
                        for (int n = 0; n <= _job._JobInfo._AlignerHistory.Count; n++)
                        {
                            string szHostAlignerHst = "HostAlignerHst" + n.ToString();
                            CreateElement(ElementLv2, szHostAlignerHst, "A", _job._JobInfo._AlignerHistory[n].Item2.Length, _job._JobInfo._AlignerHistory[n].Item2.ToString());
                        }
                    }

                    CreateElement(ElementLv1, "HostSputterHst", "A", _job._JobInfo._SputterHistory.Length, _job._JobInfo._SputterHistory);
                    CreateElement(ElementLv1, "HostBMProcHst", "A", _job._JobInfo._BMEquipmentID.Length, _job._JobInfo._BMEquipmentID);
                    CreateElement(ElementLv1, "HostOvenSkip", "A", _job._JobInfo._OvenBypass.Length, _job._JobInfo._OvenBypass);
                    CreateElement(ElementLv1, "HostCoaterSkip", "A", 0, _job._JobInfo._CoaterBypass);
                    CreateElement(ElementLv1, "HostVCRSkip", "A", 0, _job._JobInfo._VCRSkip);
                    CreateElement(ElementLv1, "HostHotRunFlagByCst", "A", 0, _job._JobInfo._HotRunFlag); //20250625 待修正
                    CreateElement(ElementLv1, "HostHotRunFlagByGls", "A", 0, _job._JobInfo._HotRunFlag); //20250625 待修正
                    CreateElement(ElementLv1, "HostReworkWeight", "A", 0, _job._JobInfo._ReworkWeight);

                    //--- Product Info
                    CreateElement(ElementLv1, "CSTID", "A", _job._SourceCassette._CassetteID.Length, _job._SourceCassette._CassetteID);
                    CreateElement(ElementLv1, "GlassID", "A", _job._GlassID._BCSGlassID.Length, _job._GlassID._BCSGlassID);
                    CreateElement(ElementLv1, "PanelInfo", "A", _job._JobInfo._PanelInfo.Length, _job._JobInfo._PanelInfo);
                    CreateElement(ElementLv1, "NGMarkReasonCode", "A", _job._JobInfo._NGMarkReasonCode.Length, _job._JobInfo._NGMarkReasonCode);
                    CreateElement(ElementLv1, "LinkKey", "A", _job._JobInfo._LinkKey.Length, _job._JobInfo._LinkKey);
                    CreateElement(ElementLv1, "RecycleCount", "A", _job._JobInfo._RecycleCount.Length, _job._JobInfo._RecycleCount);
                    //20130326 - *********:INSP Bridge to FAIC Read GlassID By Aaron.
                    CreateElement(ElementLv1, "DCRReadGlassID", "A", 0, ""); //20250625 待修正
                }

                return true;
            }
            catch (Exception ex)
            {
                // ErrHandle:
                // Log
                return false;
            }
        }
        private bool UT_Update_XMLData(XmlDocument tempDoc, string szName, string szValue)
        {
            XmlElement element = tempDoc.SelectSingleNode(szName) as XmlElement;

            if (element == null)
            {
                Console.WriteLine("Get XML Singlenode Nothing.");
            }
            else
            {
                element.Attributes["value"].Value = szValue;
            }

            return true; // Assuming you want to return true as a default behavior
        }


        private bool UT_CheckBridgeConditionAF(cBridgeCST objBridgeCST, Job _job)
        {
            string G_Log = string.Empty;
            bool verifyNgOccurred = false;
            try
            {
                Line _line = ObjectManager.LineManager.GetLine(Workbench.ServerName);
                if (_line == null)
                {
                    NLogManager.Logger.LogErrorWrite(LoggerName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", "Line object is null.");
                    return false;
                }

                if (objBridgeCST.Verify == 1)
                {
                    if (objBridgeCST.MoveOutResult == 1 || objBridgeCST.MoveOutResult == 0)
                    {
                        if (objBridgeCST.NextLinePID.Replace("0", "").Trim().ToUpper() != _line.File._NextLinePID.Replace("0", "").Trim().ToUpper())
                        {
                            G_Log = string.Format("Verify OK,But PID mismatch,Next Line PID=<{0}>", _line.File._NextLinePID);
                            G_Log += string.Format(",Verify PID=<{0}>", objBridgeCST.NextLinePID.Replace("0", "").Trim());

                            if (Workbench.Instance.LineType() == Globals.LineType.INSP_LINE_FLAG)
                            {
                                NLogManager.Logger.LogWarnWrite(LoggerName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", G_Log);
                            }
                            else
                            {
                                if (_line.File._NextLinePID.Replace("0", "").Trim().NullOrEmpty())
                                {
                                    NLogManager.Logger.LogWarnWrite(LoggerName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", G_Log);
                                }
                                else
                                {
                                    if (_line.File._NextLineGroupCRT.Replace("0", "").Trim().NullOrEmpty())
                                    {
                                        NLogManager.Logger.LogWarnWrite(LoggerName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", G_Log);
                                    }
                                    else
                                    {
                                        NLogManager.Logger.LogErrorWrite(LoggerName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", G_Log);
                                        //goto JumpVerifyNG;
                                        verifyNgOccurred = true;
                                    }
                                }
                            }
                        }

                        if (!verifyNgOccurred && objBridgeCST.NextLineGroupCRT.Replace("0", "").Trim() != _line.File._NextLineGroupCRT.Replace("0", "").Trim())
                        {
                            if (!_line.File._NextLineGroupCRT.Replace("0", "").Trim().NullOrEmpty())
                            {
                                G_Log = string.Format("Verify OK,But Group CRT mismatch,Next Line GroupCRT=<{0}>", _line.File._NextLineGroupCRT);
                                G_Log += string.Format(",Verify GroupCRT=<{0}>", objBridgeCST.NextLineGroupCRT.Replace("0", "").Trim());

                                if (Workbench.Instance.LineType() == Globals.LineType.INSP_LINE_FLAG)
                                {
                                    NLogManager.Logger.LogWarnWrite(LoggerName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", G_Log);
                                }
                                else
                                {
                                    NLogManager.Logger.LogErrorWrite(LoggerName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", G_Log);
                                    //goto JumpVerifyNG;
                                    verifyNgOccurred = true;
                                }
                            }
                        }
                
                        if (!verifyNgOccurred && !_line.File._NextLineBridgeModeON)
                        {
                            G_Log = string.Format("Next Line ID=<{0}>, Bridge Mode=<{1}>,BC Will Set Bridge CST Verify NG.", _line.File._NextLineID, _line.File._NextLineBridgeModeON);
                            NLogManager.Logger.LogErrorWrite(LoggerName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", G_Log);
                            //goto JumpVerifyNG;
                            verifyNgOccurred = true;
                        }

                        if (!verifyNgOccurred && !_line.File._NextLineHeartBit)
                        {
                            G_Log = string.Format("Next Line ID=<{0}>, Heart Beat=<{1}>,BC Will Set Bridge CST Verify NG.", _line.File._NextLineID, _line.File._NextLineHeartBit);
                            NLogManager.Logger.LogErrorWrite(LoggerName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", G_Log);
                            //goto JumpVerifyNG;
                            verifyNgOccurred = true;
                        }

                        if (verifyNgOccurred)
                        {
                            //20250620 待修正
                            //<
                            //ObjectManager.OPI.SendOPIPopMsg("Bridge Verify NG", G_Log);
                            //>
                            objBridgeCST.Verify = 2;
                            // Call MU_WF_BridgeCST(objBridgeCST)
                            SaveBridgeCSTToFile(objBridgeCST);
                            return false;
                        }


                        if (Workbench.Instance.LineType() == Globals.LineType.CNVR_LINE_FLAG)
                        {
                            _job._JobData._PortTarget = 3;
                        }
                        else if (Workbench.Instance.LineType() == Globals.LineType.DMAC_LINE_FLAG)
                        {
                            _job._JobData._PortTarget = 8;
                        }
                        else
                        {
                            _job._JobData._PortTarget = 4;
                        }
                        ObjectManager.JobManager.EnqueueSave(_job);
                        return true;
                    }
                    else
                    {
                        G_Log = string.Format("CSTID=<{0}>,MES Verify=<OK>,But the First Glass MoveOut=<NG>", _job._SourceCassette._CassetteID);
                        NLogManager.Logger.LogWarnWrite(LoggerName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", G_Log);
                        return false;
                    }
                }
                else if (objBridgeCST.Verify == 2)
                {
                    G_Log = string.Format("(Check RGBBridge Condition Error),CSTID=<{0}>,MES Verify=<NG>,If First Glass Verify NG ,then always NG", _job._SourceCassette._CassetteID);
                    NLogManager.Logger.LogWarnWrite(LoggerName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", G_Log);
                    return false;
                }
                else
                {
                    G_Log = string.Format("Verify=<{0}>,MoveOut Result=<{1}>,", objBridgeCST.Verify, objBridgeCST.MoveOutResult);
                    G_Log += "Unknown Flag can not Process";
                    NLogManager.Logger.LogErrorWrite(LoggerName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", G_Log);
                    return false;
                }

            //JumpVerifyNG:
            //    //<
            //    //ObjectManager.OPI.SendOPIPopMsg("Bridge Verify NG", G_Log);
            //    //>
            //    objBridgeCST.Verify = 2;
            //    // Call MU_WF_BridgeCST(objBridgeCST)
            //    return false;
            }
            catch (System.Exception ex)
            {
                G_Log = string.Format("Err=<{0}-{1}>", ex.Message, ex.StackTrace);
                NLogManager.Logger.LogErrorWrite(LoggerName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", G_Log);
                return false;
            }
        }
        public cBridgeCST GetBridgeCSTBySeq(string cstSeq)
        {

            string bridgeKeyNo = GetBridgeCSTKey(int.Parse(cstSeq));
            if (_entities.ContainsKey(bridgeKeyNo))
            {
                return _entities[bridgeKeyNo];
            }
            return null;
        }
        public string GetBridgeCSTKey(int szCSTSeq)
        {
            return "K" + szCSTSeq.ToString("0000");
        }

        public string CU_GetBridgeCSTKey(int szCSTSeq)
        {
            return "K" + szCSTSeq.ToString("0000");
        }

        public bool AddBridgeCST(cBridgeCST objBridgeCST, bool newWF = true)
        {
            try
            {
                // 檢查 CST Sequence 是否為 0
                if (objBridgeCST.CSTSeq == 0)
                {
                    return false;
                }

                string key = CU_GetBridgeCSTKey(objBridgeCST.CSTSeq);

                // 如果 Key 已存在，先刪除舊的
                if (_entities.ContainsKey(key))
                {
                    DeleteBridgeCSTFile(objBridgeCST);
                    string logMessage = string.Format("CST Seq duplicate in WIP,Seq#=<{0}>,Update Old to New Data", objBridgeCST.CSTSeq);
                    Log.NLogManager.Logger.LogErrorWrite(this.LoggerName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", logMessage);
                }

                // 新增到集合中
                _entities.Add(key, objBridgeCST);

                // 如果是新的工作流程，設定建立時間並寫入檔案
                if (newWF)
                {
                    objBridgeCST.CreateTime = DateTime.Now.ToString("yyyyMMddHHmmss");
                    // 呼叫寫入檔案的方法 (相當於 VB.NET 的 MU_WF_BridgeCST)
                    //20250626 待修正，已修正
                    SaveBridgeCSTToFile(objBridgeCST);
                    string logAddMessage = string.Format("Add Bridge CST to WIP,CSTSeq=<{0}>", objBridgeCST.CSTSeq);
                    Log.NLogManager.Logger.LogInfoWrite(this.LoggerName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", logAddMessage);

                }

                return true;
            }
            catch (Exception ex)
            {
                Log.NLogManager.Logger.LogErrorWrite(this.LoggerName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", ex);
                return false;
            }
        }

        private bool SaveBridgeCSTToFile(cBridgeCST objBridgeCST)
        {
            try
            {
                // 創建 BridgeCSTEntity 對象
                BridgeCSTEntity entity = new BridgeCSTEntity();
                entity._BridgeCST = objBridgeCST;

                // 設置文件名
                string filename = string.Format("BridgeCST_{0}_{1}.bin",
                    objBridgeCST._CSTID.Trim(),
                    objBridgeCST.CSTSeq.ToString("0000"));
                entity.SetFilename(filename);

                // 將文件放入保存隊列
                EnqueueSave(entity);

                return true;
            }
            catch (Exception ex)
            {
                Log.NLogManager.Logger.LogErrorWrite(this.LoggerName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", ex);
                return false;
            }
        }

    

        /// <summary>
        /// 將 VB.NET 的 DeleteBridgeCST 轉換為 C#
        /// 從集合中刪除 Bridge CST
        /// </summary>
        /// <param name="key">Bridge CST Key</param>
        /// <param name="newWF">是否為新的工作流程 (true: 刪除檔案)</param>
        /// <returns>操作是否成功</returns>
        public bool DeleteBridgeCST(string key, bool newWF = true)
        {
            try
            {
                // 檢查 Key 是否存在
                if (!_entities.ContainsKey(key))
                {
                    return false;
                }

                cBridgeCST objBridgeCST = _entities[key];

                string logMessage = string.Format("Remove Bridge CST from WIP,CSTSeq=<{0}>", objBridgeCST.CSTSeq);
                Log.NLogManager.Logger.LogInfoWrite(this.LoggerName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", logMessage);

                // 如果是新的工作流程，刪除相關檔案
                if (newWF)
                {
                    objBridgeCST.GlassNoGroup = "";
                    // 設定最後報告時間為當前時間 (對應 VB.NET 的 SYSTEMTIME 格式化)
                    objBridgeCST.LastReportTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff");

                    // 呼叫刪除檔案的方法 (相當於 VB.NET 的 MU_DF_BridgeCST)
                    //20250626 待修正，已修正
                    DeleteBridgeCSTFile(objBridgeCST);
                    //
                }

                // 從集合中移除
                _entities.Remove(key);

                return true;
            }
            catch (Exception ex)
            {
                string errorMessage = string.Format("Err=<{0}-{1}>", ex.Message, ex.StackTrace);
                Log.NLogManager.Logger.LogErrorWrite(this.LoggerName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", errorMessage);
                return false;
            }
        }

        private bool DeleteBridgeCSTFile(cBridgeCST objBridgeCST)
        {
            try
            {
                // 構建文件名
                string filename = string.Format("BridgeCST_{0}_{1}.bin",
                    objBridgeCST._CSTID.Trim(),
                    objBridgeCST.CSTSeq.ToString("0000"));

                // 構建完整路徑
                string fullPath = Path.Combine(this.BridgeCSTPath, filename);

                // 檢查文件是否存在，如果存在則刪除
                if (File.Exists(fullPath))
                {
                    File.Delete(fullPath);
                    string logMessage = string.Format("Delete Bridge CST file: {0}", fullPath);
                    Log.NLogManager.Logger.LogInfoWrite(this.LoggerName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", logMessage);
                }

                return true;
            }
            catch (Exception ex)
            {
                Log.NLogManager.Logger.LogErrorWrite(this.LoggerName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", ex);
                return false;
            }
        }

        private string _BridgeCSTPath = string.Empty; // Path for storing Bridge CST data

      
        public string BridgeCSTPath
        {
            get { return this._BridgeCSTPath.CheckPathname(); }
            set { this._BridgeCSTPath = value.Replace("{ServerName}", Workbench.ServerName).CheckPathname(true); }
        }


        private bool UT_CheckBridgeVerifySICVAF(cBridgeCST objBridgeCST, Job _job)
        {
            string szDlnGlsData = string.Empty;
            string szFileName = string.Empty;
            string G_Log = string.Empty;
            string result = string.Empty; // Function return value
            bool verifyNgOccurred = false;
            try
            {
                if (_job != null)
                {
                    //_job._JobInfo._GlassDataOld = _job._JobInfo._GlassData;
                    ObjectManager.JobManager.EnqueueSave(_job);
                }
                else
                {
                    G_Log = "Work object is null. Cannot process glass data.";
                    NLogManager.Logger.LogErrorWrite(LoggerName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", G_Log);
                    //goto JumpVerifyNG;
                    verifyNgOccurred = true;
                }

                //---Step1:Check GlassData and keep value
                if (!verifyNgOccurred)
                {
                    if (!string.IsNullOrEmpty(_job._GlassID._BCSGlassID.Trim()) &&
                        _job._JobData._CassetteSequenceNo != "0" &&
                        _job._GlassID._BCSGlassID.Trim() != new string('0', 20))
                    {
                        szDlnGlsData = _job._GlassID._BCSGlassID;
                    }
                    else
                    {
                        G_Log = string.Format("SendGlsData=<{0}>,SendCSTSeq=<{1}>Data error,BC Will Set Verify NG",
                                              _job._GlassID._BCSGlassID, _job._JobData._CassetteSequenceNo);
                        NLogManager.Logger.LogErrorWrite(LoggerName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", G_Log);
                        //goto JumpVerifyNG;
                        verifyNgOccurred = true;
                    }
                }

                if (!verifyNgOccurred)
                {
                    if (objBridgeCST.Verify == 1)
                    {
                        //---Step3:Merger Cross Line Data
                        szFileName = string.Format("{0}_{1}_{2}",
                                                   objBridgeCST._CSTID.Trim(),
                                                   objBridgeCST.CSTSeq.ToString("0000"),
                                                   _job._JobInfo._SlotNo.Trim());

                        // Original VBA: If UT_MergerBridgeDataBRIF_BySlotID(...) Then ... Else '/// GoTo JumpVerifyNG
                        // The 'Else' part was commented out in VBA, so we won't add a goto here.
                        // UT_MergerBridgeDataBRIF_BySlotID is not defined in this file.
                        // Assuming it's an external call or needs to be implemented.
                        // For now, it's commented out to avoid compilation errors.
                        // bool mergerResult = UT_MergerBridgeDataBRIF_BySlotID(objWork, objBridgeCST._CSTID, objBridgeCST.CSTSeq, long.Parse(objWork._JobInfo._LineID), szFileName, objBridgeCST.CreateTime, objWork._JobInfo._SlotNo);

                        if (UT_MergerBridgeDataBRIF_BySlotID(_job,objBridgeCST,szFileName) )
                        {}

                        //---Step4:將資料傳給MPLC_COMM寫入MPLC
                        if (!string.IsNullOrEmpty(szDlnGlsData.Trim()))
                        {
                            //---Step5:Set RGBCST Verify OK
                            result = szDlnGlsData.Trim();
                        }
                        else
                        {
                            //goto JumpVerifyNG;
                            verifyNgOccurred = true;
                        }
                    }
                    else
                    {
                        G_Log = string.Format("(Check ITESBridge Condition Error),CSTID=<{0}>,MES Verify=<NG>,If First Glass Verify NG ,then always NG",
                                              _job._SourceCassette._CassetteID);
                        NLogManager.Logger.LogWarnWrite(LoggerName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", G_Log);
                        result = _job._GlassID._BCSGlassID;
                        return false;
                    }
                }

            //JumpVerifyNG:
                if (verifyNgOccurred) // This block replaces JumpVerifyNG logic
                {
                    result = _job._GlassID._BCSGlassID;
                    //---Update ObjBridgeCST
                    Line _line = ObjectManager.LineManager.GetLine(Workbench.ServerName);
                    if (_line == null)
                    {
                        G_Log = "Line object is null in JumpVerifyNG.";
                        NLogManager.Logger.LogErrorWrite(LoggerName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", G_Log);
                        return false;
                    }

                    objBridgeCST = GetBridgeCSTBySeq(_line.File._BridgeVerifyCSTSeq.ToString());

                    if (objBridgeCST != null)
                    {
                        objBridgeCST.Verify = 2; //---0:Not Verify ,1:OK,2:NG
                        // MU_WF_BridgeCST(objBridgeCST) - Assuming this is handled by the manager or not directly needed here for persistence.
                        // If cBridgeCST needs to be explicitly saved, a method call should be added here.
                    }
                    else
                    {
                        G_Log = string.Format("Can not find the Bridge CST,CSTSeq=<{0}>", _line.File._BridgeVerifyCSTSeq);
                        NLogManager.Logger.LogErrorWrite(LoggerName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", G_Log);
                        return false;
                    }
                }
                return true;
            }
            catch (System.Exception ex)
            {
                G_Log = string.Format("Err=<{0}-{1}>", ex.Message, ex.StackTrace);
                NLogManager.Logger.LogErrorWrite(LoggerName, this.GetType().Name, MethodBase.GetCurrentMethod().Name + "()", G_Log);
                return false;
            }
        }


        public bool SaveBridgeCST(cBridgeCST objBridgeCST)
        {
            return SaveBridgeCSTToFile(objBridgeCST);
        }


        //20250626 待修正，重要確認
        #region Override Methods
        public override EntityManager.FILE_TYPE GetFileType()
        {
            return FILE_TYPE.BIN;
        }

        protected override Type GetTypeOfEntityData()
        {
            return null;
        }

        protected override Type GetTypeOfEntityFile()
        {
            return typeof(BridgeCSTEntity);
        }

        protected override void AfterInit(List<_Base> entityDatas, List<_File> entityFiles)
        {
            foreach (_File file in entityFiles)
            {
                BridgeCSTEntity bridgeCSTEntity = file as BridgeCSTEntity;
                if (bridgeCSTEntity != null)
                {
                    string key = CU_GetBridgeCSTKey(bridgeCSTEntity.CSTSeq);
                    if (!_entities.ContainsKey(key))
                    {
                        _entities.Add(key, bridgeCSTEntity._BridgeCST);
                    }
                }
            }
        }
        /// <summary>
        /// 獲取所有 Bridge CST
        /// </summary>
        /// <returns>Bridge CST 集合</returns>
        public Dictionary<string, cBridgeCST> GetAllBridgeCSTs()
        {
            return _entities;
        }

        /// <summary>
        /// 根據 CST Sequence 獲取 Bridge CST
        /// </summary>
        /// <param name="cstSeq">CST Sequence</param>
        /// <returns>Bridge CST 對象，如果不存在則返回 null</returns>
        public cBridgeCST GetBridgeCSTBySeq(int cstSeq)
        {
            string key = CU_GetBridgeCSTKey(cstSeq);
            if (_entities.ContainsKey(key))
            {
                return _entities[key];
            }
            return null;
        }

        /// <summary>
        /// 根據 CST ID 獲取 Bridge CST
        /// </summary>
        /// <param name="cstID">CST ID</param>
        /// <returns>Bridge CST 對象，如果不存在則返回 null</returns>
        public cBridgeCST GetBridgeCSTByID(string cstID)
        {
            if (string.IsNullOrEmpty(cstID))
                return null;

            foreach (var bridgeCST in _entities.Values)
            {
                if (bridgeCST._CSTID.Trim().Equals(cstID.Trim(), StringComparison.OrdinalIgnoreCase))
                {
                    return bridgeCST;
                }
            }
            return null;
        }
        #endregion
        //





       
        //public override EntityManager.FILE_TYPE GetFileType()
        //{
        //    return FILE_TYPE.BIN;
        //}

        //protected override Type GetTypeOfEntityData()
        //{
        //    return null;// typeof(CassetteEntityData);
        //}

        //protected override Type GetTypeOfEntityFile()
        //{
        //    return typeof(Cassette);
        //}

        //protected override void AfterInit(List<_Base> entityDatas, List<_File> entityFiles)
        //{
        //    foreach (_File file in entityFiles)
        //    {
        //        Cassette _cst = file as Cassette;

        //        //if (!_entities.ContainsKey(string.Format("{0}_{1}", _cst._NodeNo, _cst._PortNo)))
        //        //{
        //        //    _entities.Add(string.Format("{0}_{1}", _cst._NodeNo, _cst._PortNo), _cst);
        //        //}
        //    }
        //}

     
        protected override string GetSelectHQL()
        {
            return string.Empty;
        }

        protected override void AfterSelectDB(List<_Base> EntityDatas, string FilePath, out List<string> Filenames)
        {
            Filenames = new List<string>();
            Filenames.Add("*.Bin");
        }

        protected override _File NewEntityFile(string Filename)
        {
            return null;
        }

    
        public IList<string> GetEntityNames()
        {
            IList<string> entityNames = new List<string>();
            entityNames.Add("BridgeCSTManager");
            return entityNames;
        }


       
    }
}
